package com.jly.sharding.adapter.chain;

import com.jly.sharding.adapter.dto.ShardingDetermines;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * 排序并执行
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-12
 */
@Component
public class ProcessChainManager {

    private static List<ShardingAlgorithmProcessChain> chains;

    @Autowired
    public ProcessChainManager(List<ShardingAlgorithmProcessChain> chains) {
        if (chains == null) {
            ProcessChainManager.chains = new ArrayList<>();
            return;
        }
        chains.sort(Comparator.comparingInt(Ordered::getOrder));
        ProcessChainManager.chains = chains;
    }

    public static void process(ShardingDetermines determines, Class<? extends ShardingAlgorithmProcessChain> chainClass) {
        if (chains.isEmpty()) {
            return;
        }
        // 处理指定链
        chains.stream()
                .filter(chain -> chainClass.isAssignableFrom(chain.getClass()))
                .forEach(chain -> chain.process(determines));
    }
}
